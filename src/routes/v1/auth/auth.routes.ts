import { createRoute } from "@hono/zod-openapi";
import { z } from "zod";

// =============================================================================
// SCHEMAS
// =============================================================================

const tags = ["Authentication"];

// Base user schema from Better Auth
const userSchema = z.object({
  id: z.string(),
  email: z.string().email(),
  name: z.string(),
  image: z.string().nullable(),
  emailVerified: z.boolean(),
  createdAt: z.string(),
  updatedAt: z.string(),
  role: z.string().nullable(),
  banned: z.boolean().nullable(),
  banReason: z.string().nullable(),
  banExpires: z.string().nullable(),
}).openapi("User");

// Session schema
const sessionSchema = z.object({
  id: z.string(),
  userId: z.string(),
  expiresAt: z.string(),
  token: z.string(),
  ipAddress: z.string().nullable(),
  userAgent: z.string().nullable(),
  activeOrganizationId: z.string().nullable(),
  createdAt: z.string(),
  updatedAt: z.string(),
  impersonatedBy: z.string().nullable(),
}).openapi("Session");

// Organization schema
const organizationSchema = z.object({
  id: z.string(),
  name: z.string(),
  slug: z.string().nullable(),
  logo: z.string().nullable(),
  companyType: z.string().nullable(),
  metadata: z.string().nullable(),
  createdAt: z.string(),
}).openapi("Organization");

// User profile schema
const userProfileSchema = z.object({
  id: z.string(),
  userId: z.string().nullable(),
  organizationId: z.string().nullable(),
  firstName: z.string().nullable(),
  lastName: z.string().nullable(),
  displayName: z.string().nullable(),
  phone: z.string().nullable(),
  licenseNumber: z.string().nullable(),
  website: z.string().nullable(),
  businessAddress: z.string().nullable(),
  marketingConsent: z.boolean().nullable(),
  termsAccepted: z.boolean().nullable(),
  termsAcceptedAt: z.string().nullable(),
  avatarUrl: z.string().nullable(),
  bio: z.string().nullable(),
  specialties: z.array(z.string()).nullable(),
  isActive: z.boolean(),
  preferences: z.record(z.any()).nullable(),
  createdAt: z.string(),
  updatedAt: z.string(),
  joinedAt: z.string().nullable(),
  lastLoginAt: z.string().nullable(),
}).openapi("UserProfile");

// Password validation schema
const passwordSchema = z.string()
  .min(8, "Password must be at least 8 characters")
  .regex(/[A-Z]/, "Password must contain at least one uppercase letter")
  .regex(/[a-z]/, "Password must contain at least one lowercase letter")
  .regex(/[0-9]/, "Password must contain at least one number");

// Sign up request schema
const signUpRequestSchema = z.object({
  // Required fields
  first_name: z.string().min(1, "First name is required"),
  last_name: z.string().min(1, "Last name is required"),
  email: z.string().email("Invalid email format"),
  company_name: z.string().min(1, "Company name is required"),
  company_type: z.enum(["individual", "team", "firm"]).default("individual"),
  password: passwordSchema,
  confirm_password: z.string(),
  terms_accepted: z.boolean().refine(val => val === true, {
    message: "You must accept the Terms & Conditions"
  }),

  // Optional fields
  phone: z.string().optional(),
  license_number: z.string().optional(),
  website: z.string().url().optional().or(z.literal("")),
  business_address: z.string().optional(),
  marketing_consent: z.boolean().default(false),

  // Optional Better Auth fields
  image: z.string().url().optional(),
  callbackURL: z.string().url().optional(),
  rememberMe: z.boolean().default(true),
}).refine((data) => data.password === data.confirm_password, {
  message: "Passwords do not match",
  path: ["confirm_password"],
}).transform((data) => {
  // Transform to match Better Auth expected format
  const { first_name, last_name, confirm_password, terms_accepted, business_address, marketing_consent, ...rest } = data;
  return {
    ...rest,
    name: `${first_name} ${last_name}`,
    first_name,
    last_name,
    business_address,
    marketing_consent,
    terms_accepted,
  };
}).openapi("SignUpRequest");

// Sign in request schema
const signInRequestSchema = z.object({
  email: z.string().email("Invalid email format"),
  password: z.string().min(1, "Password is required"),
  callbackURL: z.string().url().optional(),
  rememberMe: z.boolean().default(true),
}).openapi("SignInRequest");

// Auth response schema (cookie-based, session data included in response)
const authResponseSchema = z.object({
  user: userSchema,
  session: sessionSchema.nullable(),
  organization: organizationSchema.nullable(),
  profile: userProfileSchema.nullable(),
}).openapi("AuthResponse");

// Error response schema
const errorResponseSchema = z.object({
  message: z.string(),
  code: z.string().optional(),
}).openapi("ErrorResponse");

// =============================================================================
// ROUTE DEFINITIONS
// =============================================================================

// POST /v1/auth/sign-up/email
export const signUpEmailRoute = createRoute({
  path: "/v1/auth/sign-up/email",
  method: "post",
  summary: "Sign up with email and password",
  description: "Create a new user account with email/password and organization data. Authentication is handled via secure session cookies. Returns user, session (with token, expiry, etc.), and organization information.",
  tags,
  request: {
    body: {
      content: {
        "application/json": {
          schema: signUpRequestSchema,
        },
      },
    },
  },
  responses: {
    200: {
      content: {
        "application/json": {
          schema: authResponseSchema,
        },
      },
      description: "User created successfully",
    },
    400: {
      content: {
        "application/json": {
          schema: errorResponseSchema,
        },
      },
      description: "Bad request - Invalid input data",
    },
    409: {
      content: {
        "application/json": {
          schema: errorResponseSchema,
        },
      },
      description: "Conflict - User already exists",
    },
    500: {
      content: {
        "application/json": {
          schema: errorResponseSchema,
        },
      },
      description: "Internal server error",
    },
  },
});

// POST /v1/auth/sign-in/email
export const signInEmailRoute = createRoute({
  path: "/v1/auth/sign-in/email",
  method: "post",
  summary: "Sign in with email and password",
  description: "Authenticate user with email/password. Authentication is handled via secure session cookies. Returns user, session (with token, expiry, etc.), and organization information.",
  tags,
  request: {
    body: {
      content: {
        "application/json": {
          schema: signInRequestSchema,
        },
      },
    },
  },
  responses: {
    200: {
      content: {
        "application/json": {
          schema: authResponseSchema,
        },
      },
      description: "User authenticated successfully",
    },
    400: {
      content: {
        "application/json": {
          schema: errorResponseSchema,
        },
      },
      description: "Bad request - Invalid credentials",
    },
    401: {
      content: {
        "application/json": {
          schema: errorResponseSchema,
        },
      },
      description: "Unauthorized - Invalid email or password",
    },
    500: {
      content: {
        "application/json": {
          schema: errorResponseSchema,
        },
      },
      description: "Internal server error",
    },
  },
});

// =============================================================================
// ROUTER ASSEMBLY
// =============================================================================

import { createRouter } from "@/lib/create-app";
import * as handlers from "./auth.controller";

export const authRouter = createRouter()
  .openapi(signUpEmailRoute, handlers.signUpEmail)
  .openapi(signInEmailRoute, handlers.signInEmail);

// Export router as default for backward compatibility during migration
export default authRouter;
