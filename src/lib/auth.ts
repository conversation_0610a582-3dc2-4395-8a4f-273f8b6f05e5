import { betterAuth } from "better-auth";
import { admin, organization, openAPI } from "better-auth/plugins";
import { drizzleAdapter } from "better-auth/adapters/drizzle";
import db from "@/db";
import env from "@/env";

// Minimal Better Auth server instance. It will expose handlers we can mount under /api/auth.
export const auth = betterAuth({
  database: drizzleAdapter(db, {
    provider: "pg",
  }),
  baseURL: process.env.BETTER_AUTH_URL || `http://localhost:${env.PORT}`,
  secret: process.env.BETTER_AUTH_SECRET || "dev-secret-change-me",
  advanced: {
    cookiePrefix: "rendyr",
    // Ensure cookies work in development
    crossSubDomainCookies: {
      enabled: true,
      domain: process.env.NODE_ENV === 'development' ? 'localhost' : undefined
    }
  },
  // Enable email/password auth as per server usage guide
  emailAndPassword: {
    enabled: true,
    autoSignIn: true ,
    // requireEmailVerification: true,
  },
  // Allow requests from these origins to interact with Better Auth (e.g. cookies, redirects)
  trustedOrigins: env.CORS_ORIGINS,
  // Session configuration
  session: {
    // Disable cookie cache temporarily to debug session issues
    // cookieCache: { enabled: true, maxAge: 5 * 60 },
    // Ensure session cookies are properly configured
    expiresIn: 60 * 60 * 24 * 7, // 7 days
    updateAge: 60 * 60 * 24, // refresh daily
  },
  plugins: [admin(), organization(), openAPI()],
});

export type BetterAuth = typeof auth;


