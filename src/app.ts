import configureOpenAPI from "@/lib/configure-open-api";
import createApp from "@/lib/create-app";
import index from "@/routes/index.route";
import apiClient from "@/routes/api-client.route";
import { authRouter } from "@/routes/v1/auth/auth.routes";
import { auth } from "@/lib/auth";
import { filesRouter } from "@/routes/v1/files/files.routes";
import { profilesRouter } from "@/routes/v1/profiles/profiles.routes";
import { listingsRouter } from "@/routes/v1/listings/listings.routes";
import { logsRouter } from "@/routes/v1/logs/logs.routes";
// import { workspacesRouter } from "@/routes/v1/workspaces/workspaces.routes";
// import { serveStatic } from "hono/serve-static";

const app = createApp();

configureOpenAPI(app);

// Mount Better Auth under /api/auth/*
app.all("/api/auth/*", async (c) => {
  return auth.handler(c.req.raw);
});

// Add root-level GET /get-session endpoint for client convenience
app.get("/get-session", async (c) => {
  try {
    // Debug: Log request details
    if (process.env.NODE_ENV === 'development') {
      console.log('🔍 /get-session - cookies:', c.req.header('cookie'));
      console.log('🔍 /get-session - headers:', Object.fromEntries(c.req.raw.headers.entries()));
    }

    // Use Better Auth's session validation with proper request context
    const session = await auth.api.getSession({
      headers: c.req.raw.headers,
      // Ensure we're using the same request context
      query: c.req.query()
    });

    if (process.env.NODE_ENV === 'development') {
      console.log('🔍 /get-session - session result:', {
        hasSession: !!session?.session,
        hasUser: !!session?.user,
        userId: session?.user?.id,
      });
    }

    if (session?.user && session?.session) {
      return c.json({
        session: session.session,
        user: session.user,
      });
    } else {
      return c.json({
        session: null,
        user: null,
      });
    }
  } catch (error) {
    console.error('Get session error:', error);
    return c.json({
      session: null,
      user: null,
    });
  }
});

// Serve local uploads under /uploads/* from public/uploads
// app.use("/uploads/*", serveStatic({ root: "./public" }));

const routes = [
  index,
  apiClient,
  authRouter,
  profilesRouter,
  filesRouter,
  listingsRouter,
  logsRouter,
  // workspacesRouter,
] as const;

routes.forEach((route) => {
  app.route("/", route);
});

export type AppType = typeof routes[number];

export default app;
