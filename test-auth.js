#!/usr/bin/env node

// Test script to debug authentication flow
const BASE_URL = 'http://localhost:9999';

async function testAuthFlow() {
  console.log('🔍 Testing authentication flow...\n');

  const testEmail = `debug-${Date.now()}@example.com`;
  const testPassword = 'Password123!';

  // Test 0: Sign up first (in case user doesn't exist)
  console.log('0. Testing sign-up...');
  try {
    const signUpResponse = await fetch(`${BASE_URL}/v1/auth/sign-up/email`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        first_name: 'Debug',
        last_name: 'User',
        email: testEmail,
        company_name: 'Debug Company',
        company_type: 'individual',
        password: testPassword,
        confirm_password: testPassword,
        terms_accepted: true,
        rememberMe: true
      }),
      credentials: 'include'
    });

    console.log('Sign-up status:', signUpResponse.status);
    if (signUpResponse.ok) {
      const signUpData = await signUpResponse.json();
      console.log('Sign-up successful!');

      // Extract cookies from sign-up response
      const signUpCookies = signUpResponse.headers.get('set-cookie');
      console.log('Sign-up Set-Cookie headers:', signUpCookies);

      // Test get-session immediately after sign-up
      console.log('\n0.5. Testing get-session after sign-up...');
      const sessionAfterSignUp = await fetch(`${BASE_URL}/get-session`, {
        method: 'GET',
        headers: {
          'Cookie': signUpCookies || ''
        },
        credentials: 'include'
      });

      const sessionAfterSignUpData = await sessionAfterSignUp.json();
      console.log('Get-session after sign-up:', JSON.stringify(sessionAfterSignUpData, null, 2));

    } else {
      const errorData = await signUpResponse.json();
      console.log('Sign-up error (might already exist):', JSON.stringify(errorData, null, 2));
    }
  } catch (error) {
    console.log('Sign-up error:', error.message);
  }

  // Test 1: Sign in
  console.log('\n1. Testing sign-in...');
  try {
    const signInResponse = await fetch(`${BASE_URL}/v1/auth/sign-in/email`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: testEmail,
        password: testPassword,
        rememberMe: true
      }),
      credentials: 'include' // Important for cookies
    });

    console.log('Sign-in status:', signInResponse.status);
    console.log('Sign-in headers:', Object.fromEntries(signInResponse.headers.entries()));
    
    if (signInResponse.ok) {
      const signInData = await signInResponse.json();
      console.log('Sign-in response:', JSON.stringify(signInData, null, 2));
      
      // Extract cookies from response
      const setCookieHeaders = signInResponse.headers.get('set-cookie');
      console.log('Set-Cookie headers:', setCookieHeaders);
      
      // Test 2: Get session with cookies
      console.log('\n2. Testing get-session...');
      const sessionResponse = await fetch(`${BASE_URL}/get-session`, {
        method: 'GET',
        headers: {
          'Cookie': setCookieHeaders || ''
        },
        credentials: 'include'
      });
      
      console.log('Get-session status:', sessionResponse.status);
      const sessionData = await sessionResponse.json();
      console.log('Get-session response:', JSON.stringify(sessionData, null, 2));
      
    } else {
      const errorData = await signInResponse.json();
      console.log('Sign-in error:', errorData);
    }
    
  } catch (error) {
    console.error('Test error:', error);
  }
}

testAuthFlow();
