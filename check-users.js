#!/usr/bin/env node

import postgres from 'postgres';
import { config } from 'dotenv';

// Load environment variables
config();

const client = postgres(process.env.DATABASE_URL);

async function checkUsers() {
  try {
    console.log('🔍 Checking existing users in database...\n');

    // Query users table
    const users = await client`
      SELECT id, email, name, email_verified, created_at
      FROM "user"
      ORDER BY created_at DESC
      LIMIT 10
    `;

    console.log('Found users:');
    console.table(users);

    // Query accounts table to see authentication methods
    const accounts = await client`
      SELECT user_id, provider_id, account_id
      FROM "account"
      LIMIT 10
    `;

    console.log('\nFound accounts:');
    console.table(accounts);

  } catch (error) {
    console.error('Error checking users:', error);
  } finally {
    await client.end();
  }
}

checkUsers();
